# 代码重构指南

## 已完成的改进

### 1. ✅ 添加了String转Long的反序列化配置
- 更新了 `JacksonConfig.java`，添加了 `StringToLongDeserializer`
- 现在可以直接在控制器中使用 `@PathVariable Long id`

### 2. ✅ 实现了基于ThreadLocal的用户上下文
- 创建了 `UserContext.java` 工具类
- 更新了 `JwtInterceptor.java` 使用 `UserContext.setUserId()`
- 在拦截器的 `afterCompletion` 方法中清除ThreadLocal

### 3. ✅ 创建了业务异常类
- 创建了 `BusinessException.java`
- 创建了 `GlobalExceptionHandler.java` 全局异常处理器

### 4. ✅ 更新了Swagger配置
- 添加了 `ModelConverter` 将Long类型在API文档中显示为String
- 解决了API文档中Long字段显示问题

### 5. ✅ 部分控制器已更新
- `AuthController.java` - 使用构造器注入和UserContext
- `AllianceController.java` - 使用构造器注入、Long PathVariable和UserContext
- `GameAccountController.java` - 使用构造器注入、Long PathVariable和UserContext

### 6. ✅ 部分服务类已更新
- `AuthService.java` - 使用构造器注入和BusinessException
- `AllianceService.java` - 部分更新了构造器注入和BusinessException

## 需要完成的剩余工作

### 1. 更新剩余的控制器类

需要更新以下控制器：
- `AllianceMemberController.java`
- `WarController.java`
- `WarGroupController.java`
- `QueryController.java`

对每个控制器进行以下更改：
1. 移除 `@Autowired`，使用构造器注入
2. 将所有 `@PathVariable String id` 改为 `@PathVariable Long id`
3. 移除 `HttpServletRequest` 参数，使用 `UserContext.getUserId()`
4. 移除 `try-catch` 块（异常由GlobalExceptionHandler处理）

### 2. 更新剩余的服务类

需要更新以下服务类：
- `GameAccountService.java`
- `AllianceMemberService.java`
- `WarService.java`
- `WarGroupService.java`
- `QueryService.java`

对每个服务类进行以下更改：
1. 移除所有 `@Autowired`，使用构造器注入
2. 将所有 `throw new RuntimeException()` 改为 `throw new BusinessException()`

### 3. 更新配置类

需要更新：
- `WeChatConfig.java` - 移除@Autowired，使用构造器注入

### 4. 更新工具类

需要更新：
- `AllianceCodeGenerator.java` - 如果有依赖注入的话

## 批量替换命令

可以使用以下命令进行批量替换：

```bash
# 替换RuntimeException为BusinessException
find src/main/java -name "*.java" -exec sed -i 's/throw new RuntimeException(/throw new BusinessException(/g' {} \;

# 替换@Autowired为构造器注入（需要手动处理）
# 这个需要手动处理，因为涉及到构造器的创建

# 替换@PathVariable String为@PathVariable Long
find src/main/java -name "*.java" -exec sed -i 's/@PathVariable String \([a-zA-Z]*Id\)/@PathVariable Long \1/g' {} \;
```

## 验证步骤

完成所有更改后，需要验证：

1. **编译检查**：
   ```bash
   mvn compile
   ```

2. **运行测试**：
   ```bash
   mvn test
   ```

3. **启动应用**：
   ```bash
   mvn spring-boot:run
   ```

4. **检查Swagger文档**：
   访问 `http://localhost:8080/swagger-ui.html`
   验证Long字段在API文档中显示为String类型

5. **测试API**：
   - 测试登录接口
   - 测试需要认证的接口
   - 验证异常处理是否正常工作

## 注意事项

1. **ThreadLocal清理**：确保在请求结束后清理ThreadLocal，避免内存泄漏
2. **异常处理**：业务异常使用BusinessException，系统异常由GlobalExceptionHandler统一处理
3. **构造器注入**：确保所有依赖都通过构造器注入，提高代码的可测试性
4. **Long类型处理**：确保前端发送的String类型ID能正确转换为Long类型

## 完成后的优势

1. **更好的异常处理**：统一的异常处理机制，用户友好的错误信息
2. **更简洁的代码**：移除了大量的try-catch块和HttpServletRequest参数
3. **更好的可测试性**：构造器注入使得单元测试更容易编写
4. **类型安全**：直接使用Long类型，避免了手动转换的错误
5. **线程安全**：基于ThreadLocal的用户上下文，避免了线程安全问题
