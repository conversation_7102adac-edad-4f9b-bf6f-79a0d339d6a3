# 王朝接口完善文档

## 概述
本次更新完善了王朝管理系统的三个核心接口：
1. 更新王朝接口
2. 删除王朝接口（删除所有相关数据）
3. 账号退出王朝接口

## 新增功能

### 1. 更新王朝接口

**接口路径**: `PUT /api/dynasties/{dynastyId}`

**功能描述**: 允许王朝天子更新王朝的基本信息

**权限验证**: 只有王朝天子才能执行此操作

**请求参数**:
```json
{
  "name": "新的王朝名称"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "王朝信息更新成功",
  "data": {
    "id": 1,
    "name": "新的王朝名称",
    "code": "ABC123",
    "serverId": 1,
    "emperorId": 1,
    "grabEnabled": false
  }
}
```

### 2. 删除王朝接口

**接口路径**: `DELETE /api/dynasties/{dynastyId}`

**功能描述**: 删除王朝及所有相关数据

**权限验证**: 只有王朝天子才能执行此操作

**删除的数据包括**:
- 清空所有成员的王朝关联（将成员的dynastyId设为null）
- 删除所有官职抢夺记录
- 删除所有官职配置
- 删除王朝本身

**响应示例**:
```json
{
  "success": true,
  "message": "王朝删除成功",
  "data": null
}
```

### 3. 账号退出王朝接口

**接口路径**: `POST /api/dynasties/accounts/{accountId}/leave`

**功能描述**: 允许用户让自己的账号退出王朝

**权限验证**: 只能操作自己的账号

**业务逻辑**:
- 验证账号存在且属于当前用户
- 验证账号已加入王朝
- 将账号的dynastyId设为null

**响应示例**:
```json
{
  "success": true,
  "message": "账号退出王朝成功",
  "data": {
    "id": 1,
    "accountName": "测试账号",
    "dynastyId": null,
    // ... 其他账号信息
  }
}
```

## 实现细节

### DynastyService 新增方法

1. **updateDynasty(Long dynastyId, UpdateDynastyRequest request)**
   - 验证天子权限
   - 更新王朝名称
   - 返回更新后的王朝信息

2. **deleteDynasty(Long dynastyId)**
   - 验证天子权限
   - 按顺序清理相关数据：成员关联 → 抢夺记录 → 官职配置 → 王朝本身
   - 使用事务确保数据一致性

3. **leaveDynasty(Long accountId)**
   - 验证账号所有权
   - 验证账号已加入王朝
   - 清空账号的王朝关联

### 错误处理

所有接口都包含完整的错误处理：
- 权限验证失败
- 资源不存在
- 业务规则违反

### 事务管理

所有修改操作都使用 `@Transactional` 注解确保数据一致性。

## 测试覆盖

为所有新功能编写了完整的单元测试：
- 成功场景测试
- 权限验证测试
- 异常情况测试

测试文件: `src/test/java/com/app/gamehub/service/DynastyServiceTest.java`

## API 文档

所有接口都包含完整的 Swagger 注解，支持自动生成 API 文档。

## 安全考虑

1. **权限控制**: 严格的权限验证，确保只有授权用户才能执行相应操作
2. **数据完整性**: 删除王朝时确保所有相关数据都被正确清理
3. **事务安全**: 使用数据库事务确保操作的原子性

## 使用示例

### 更新王朝
```bash
curl -X PUT /api/dynasties/1 \
  -H "Content-Type: application/json" \
  -d '{"name": "新王朝名称"}'
```

### 删除王朝
```bash
curl -X DELETE /api/dynasties/1
```

### 账号退出王朝
```bash
curl -X POST /api/dynasties/accounts/1/leave
```
