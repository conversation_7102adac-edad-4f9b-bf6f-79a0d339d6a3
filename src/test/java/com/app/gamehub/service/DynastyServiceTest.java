package com.app.gamehub.service;

import com.app.gamehub.dto.CreateDynastyRequest;
import com.app.gamehub.dto.JoinDynastyRequest;
import com.app.gamehub.entity.*;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.repository.*;
import com.app.gamehub.util.AllianceCodeGenerator;
import com.app.gamehub.util.UserContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DynastyServiceTest {

    @Mock
    private DynastyRepository dynastyRepository;
    
    @Mock
    private DynastyPositionRepository dynastyPositionRepository;
    
    @Mock
    private PositionGrabRepository positionGrabRepository;
    
    @Mock
    private GameAccountRepository gameAccountRepository;
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private AllianceCodeGenerator codeGenerator;

    @InjectMocks
    private DynastyService dynastyService;

    private Long userId = 1L;
    private Integer serverId = 1;
    private String dynastyCode = "ABC123";

    @BeforeEach
    void setUp() {
        // 模拟用户上下文
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
        }
    }

    @Test
    void createDynasty_Success() {
        // Given
        CreateDynastyRequest request = new CreateDynastyRequest();
        request.setName("大汉王朝");
        request.setServerId(serverId);

        when(userRepository.existsById(userId)).thenReturn(true);
        when(dynastyRepository.existsByEmperorIdAndServerId(userId, serverId)).thenReturn(false);
        when(codeGenerator.generateCode()).thenReturn(dynastyCode);
        when(dynastyRepository.existsByCode(dynastyCode)).thenReturn(false);
        
        Dynasty savedDynasty = new Dynasty();
        savedDynasty.setId(1L);
        savedDynasty.setName(request.getName());
        savedDynasty.setCode(dynastyCode);
        savedDynasty.setServerId(serverId);
        savedDynasty.setEmperorId(userId);
        savedDynasty.setGrabEnabled(false);
        
        when(dynastyRepository.save(any(Dynasty.class))).thenReturn(savedDynasty);

        // When
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            Dynasty result = dynastyService.createDynasty(request);

            // Then
            assertNotNull(result);
            assertEquals("大汉王朝", result.getName());
            assertEquals(dynastyCode, result.getCode());
            assertEquals(serverId, result.getServerId());
            assertEquals(userId, result.getEmperorId());
            assertFalse(result.getGrabEnabled());

            verify(dynastyRepository).save(any(Dynasty.class));
            verify(dynastyPositionRepository, times(2)).save(any(DynastyPosition.class)); // 两种官职类型
        }
    }

    @Test
    void createDynasty_UserNotExists_ThrowsException() {
        // Given
        CreateDynastyRequest request = new CreateDynastyRequest();
        request.setName("大汉王朝");
        request.setServerId(serverId);

        when(userRepository.existsById(userId)).thenReturn(false);

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> dynastyService.createDynasty(request));
            assertEquals("用户不存在", exception.getMessage());
        }
    }

    @Test
    void createDynasty_DynastyAlreadyExists_ThrowsException() {
        // Given
        CreateDynastyRequest request = new CreateDynastyRequest();
        request.setName("大汉王朝");
        request.setServerId(serverId);

        when(userRepository.existsById(userId)).thenReturn(true);
        when(dynastyRepository.existsByEmperorIdAndServerId(userId, serverId)).thenReturn(true);

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> dynastyService.createDynasty(request));
            assertEquals("您在该区已创建王朝，每个用户在每个区只能创建一个王朝", exception.getMessage());
        }
    }

    @Test
    void getDynastyByCode_Success() {
        // Given
        Dynasty dynasty = new Dynasty();
        dynasty.setId(1L);
        dynasty.setCode(dynastyCode);
        dynasty.setName("大汉王朝");

        when(dynastyRepository.findByCode(dynastyCode)).thenReturn(Optional.of(dynasty));

        // When
        Dynasty result = dynastyService.getDynastyByCode(dynastyCode);

        // Then
        assertNotNull(result);
        assertEquals(dynastyCode, result.getCode());
        assertEquals("大汉王朝", result.getName());
    }

    @Test
    void getDynastyByCode_NotFound_ThrowsException() {
        // Given
        when(dynastyRepository.findByCode(dynastyCode)).thenReturn(Optional.empty());

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> dynastyService.getDynastyByCode(dynastyCode));
        assertEquals("王朝不存在", exception.getMessage());
    }

    @Test
    void joinDynasty_Success() {
        // Given
        Long accountId = 1L;
        JoinDynastyRequest request = new JoinDynastyRequest();
        request.setDynastyCode(dynastyCode);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(userId);
        account.setServerId(serverId);
        account.setDynastyId(null);

        Dynasty dynasty = new Dynasty();
        dynasty.setId(1L);
        dynasty.setCode(dynastyCode);
        dynasty.setServerId(serverId);

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));
        when(dynastyRepository.findByCode(dynastyCode)).thenReturn(Optional.of(dynasty));
        when(gameAccountRepository.save(any(GameAccount.class))).thenReturn(account);

        // When
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            GameAccount result = dynastyService.joinDynasty(accountId, request);

            // Then
            assertNotNull(result);
            assertEquals(dynasty.getId(), result.getDynastyId());
            verify(gameAccountRepository).save(account);
        }
    }

    @Test
    void joinDynasty_AccountNotBelongsToUser_ThrowsException() {
        // Given
        Long accountId = 1L;
        Long otherUserId = 2L;
        JoinDynastyRequest request = new JoinDynastyRequest();
        request.setDynastyCode(dynastyCode);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(otherUserId); // 不同的用户ID

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> dynastyService.joinDynasty(accountId, request));
            assertEquals("无权操作此账号", exception.getMessage());
        }
    }

    @Test
    void joinDynasty_AccountAlreadyInDynasty_ThrowsException() {
        // Given
        Long accountId = 1L;
        JoinDynastyRequest request = new JoinDynastyRequest();
        request.setDynastyCode(dynastyCode);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(userId);
        account.setDynastyId(2L); // 已经加入了王朝

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> dynastyService.joinDynasty(accountId, request));
            assertEquals("账号已加入王朝，无法重复加入", exception.getMessage());
        }
    }

    @Test
    void joinDynasty_DifferentServer_ThrowsException() {
        // Given
        Long accountId = 1L;
        JoinDynastyRequest request = new JoinDynastyRequest();
        request.setDynastyCode(dynastyCode);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(userId);
        account.setServerId(1);
        account.setDynastyId(null);

        Dynasty dynasty = new Dynasty();
        dynasty.setId(1L);
        dynasty.setCode(dynastyCode);
        dynasty.setServerId(2); // 不同的区号

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));
        when(dynastyRepository.findByCode(dynastyCode)).thenReturn(Optional.of(dynasty));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> dynastyService.joinDynasty(accountId, request));
            assertEquals("账号只能加入同一个区的王朝", exception.getMessage());
        }
    }

    @Test
    void validateEmperor_Success() {
        // Given
        Long dynastyId = 1L;
        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setEmperorId(userId);

        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            assertDoesNotThrow(() -> dynastyService.validateEmperor(dynastyId));
        }
    }

    @Test
    void validateEmperor_NotEmperor_ThrowsException() {
        // Given
        Long dynastyId = 1L;
        Long otherUserId = 2L;
        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setEmperorId(otherUserId);

        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> dynastyService.validateEmperor(dynastyId));
            assertEquals("只有天子才能执行此操作", exception.getMessage());
        }
    }
}
