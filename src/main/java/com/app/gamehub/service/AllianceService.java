package com.app.gamehub.service;

import com.app.gamehub.dto.CreateAllianceRequest;
import com.app.gamehub.dto.TransferAllianceRequest;
import com.app.gamehub.dto.UpdateAllianceRequest;
import com.app.gamehub.entity.Alliance;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.repository.AllianceRepository;
import com.app.gamehub.repository.GameAccountRepository;
import com.app.gamehub.repository.UserRepository;
import com.app.gamehub.util.AllianceCodeGenerator;
import com.app.gamehub.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class AllianceService {

  private final AllianceRepository allianceRepository;
  private final UserRepository userRepository;
  private final GameAccountRepository gameAccountRepository;
  private final AllianceCodeGenerator codeGenerator;

  public AllianceService(AllianceRepository allianceRepository, UserRepository userRepository,
                         GameAccountRepository gameAccountRepository, AllianceCodeGenerator codeGenerator) {
    this.allianceRepository = allianceRepository;
    this.userRepository = userRepository;
    this.gameAccountRepository = gameAccountRepository;
    this.codeGenerator = codeGenerator;
  }

  @Transactional
  public Alliance createAlliance(CreateAllianceRequest request) {
    Long userId = UserContext.getUserId();
    // 验证用户是否存在
    if (!userRepository.existsById(userId)) {
      throw new BusinessException("用户不存在");
    }

    // 生成唯一的联盟编码
    String code;
    do {
      code = codeGenerator.generateCode();
    } while (allianceRepository.existsByCode(code));

    Alliance alliance = new Alliance();
    alliance.setName(request.getName());
    alliance.setCode(code);
    alliance.setServerId(request.getServerId());
    alliance.setLeaderId(userId);

    return allianceRepository.save(alliance);
  }

  @Transactional
  public Alliance updateAlliance(Long allianceId, UpdateAllianceRequest request) {
    Alliance alliance = allianceRepository.findById(allianceId)
        .orElseThrow(() -> new BusinessException("联盟不存在"));

    // 验证是否为盟主
    if (!alliance.getLeaderId().equals(UserContext.getUserId())) {
      throw new BusinessException("只有盟主可以更新联盟信息");
    }

    if (request.getName() != null && !request.getName().trim().isEmpty()) {
      alliance.setName(request.getName().trim());
    }

    if (request.getCode() != null && !request.getCode().trim().isEmpty()) {
      String newCode = request.getCode().trim().toUpperCase();
      if (!alliance.getCode().equals(newCode)) {
        if (allianceRepository.existsByCode(newCode)) {
          throw new BusinessException("联盟编码已存在");
        }
        alliance.setCode(newCode);
      }
    }

    return allianceRepository.save(alliance);
  }

  @Transactional
  public void deleteAlliance(Long allianceId) {
    Long userId = UserContext.getUserId();
    Alliance alliance = allianceRepository.findById(allianceId)
        .orElseThrow(() -> new BusinessException("联盟不存在"));

    // 验证是否为盟主
    if (!alliance.getLeaderId().equals(userId)) {
      throw new BusinessException("只有盟主可以删除联盟");
    }

    // 检查是否还有成员
    long memberCount = allianceRepository.countMembersByAllianceId(allianceId);
    if (memberCount > 0) {
      throw new BusinessException("联盟还有成员，无法删除");
    }

    allianceRepository.delete(alliance);
  }

  @Transactional
  public Alliance transferAlliance(Long allianceId, TransferAllianceRequest request) {
    Long userId = UserContext.getUserId();
    Alliance alliance = allianceRepository.findById(allianceId)
        .orElseThrow(() -> new BusinessException("联盟不存在"));

    // 验证是否为盟主
    if (!alliance.getLeaderId().equals(userId)) {
      throw new BusinessException("只有盟主可以转交联盟");
    }

    Long newLeaderId = request.getNewLeaderId();

    // 验证新盟主是否存在
    if (!userRepository.existsById(newLeaderId)) {
      throw new BusinessException("新盟主用户不存在");
    }

    // 验证新盟主是否为联盟成员
    boolean isNewLeaderMember = gameAccountRepository.existsByUserIdAndServerIdAndAllianceIdIsNotNull(
        newLeaderId, alliance.getServerId());
    if (!isNewLeaderMember) {
      throw new BusinessException("新盟主必须是联盟成员");
    }

    alliance.setLeaderId(newLeaderId);
    return allianceRepository.save(alliance);
  }

  public List<Alliance> getUserAlliances() {
    Long userId = UserContext.getUserId();
    return allianceRepository.findByLeaderIdOrderByServerIdDesc(userId);
  }

  public Alliance getAllianceById(Long allianceId) {
    return allianceRepository.findById(allianceId)
        .orElseThrow(() -> new BusinessException("联盟不存在"));
  }

  public Alliance getAllianceByCode(String code) {
    return allianceRepository.findByCode(code.toUpperCase())
        .orElseThrow(() -> new BusinessException("联盟不存在"));
  }
}
