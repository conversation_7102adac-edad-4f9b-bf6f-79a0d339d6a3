package com.app.gamehub.service;

import com.app.gamehub.dto.*;
import com.app.gamehub.entity.*;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.repository.*;
import com.app.gamehub.util.UserContext;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** 官职抢夺服务类 */
@Slf4j
@Service
public class PositionGrabService {

  private final DynastyRepository dynastyRepository;
  private final DynastyPositionRepository dynastyPositionRepository;
  private final PositionGrabRepository positionGrabRepository;
  private final GameAccountRepository gameAccountRepository;

  public PositionGrabService(
      DynastyRepository dynastyRepository,
      DynastyPositionRepository dynastyPositionRepository,
      PositionGrabRepository positionGrabRepository,
      GameAccountRepository gameAccountRepository) {
    this.dynastyRepository = dynastyRepository;
    this.dynastyPositionRepository = dynastyPositionRepository;
    this.positionGrabRepository = positionGrabRepository;
    this.gameAccountRepository = gameAccountRepository;
  }

  /** 设置官职抢夺时间 */
  @Transactional
  public DynastyPosition setPositionGrabTime(Long dynastyId, SetPositionGrabTimeRequest request) {
    // 验证用户是否为天子
    validateEmperor(dynastyId);

    // 验证时间设置的合理性
    validateGrabTimeSettings(request);

    // 查找或创建官职配置
    DynastyPosition position =
        dynastyPositionRepository
            .findByDynastyIdAndPositionType(dynastyId, request.getPositionType())
            .orElseThrow(() -> new BusinessException("官职配置不存在"));

    // 更新配置
    position.setGrabStartTime(request.getGrabStartTime());
    position.setGrabEndTime(request.getGrabEndTime());
    position.setDutyDate(request.getDutyDate());

    // 处理禁用时段
    String disabledSlots = "";
    if (request.getDisabledTimeSlots() != null && !request.getDisabledTimeSlots().isEmpty()) {
      disabledSlots =
          request.getDisabledTimeSlots().stream()
              .map(String::valueOf)
              .collect(Collectors.joining(","));
    }
    position.setDisabledTimeSlots(disabledSlots);

    DynastyPosition savedPosition = dynastyPositionRepository.save(position);

    // 清空该官职的历史抢夺结果
    positionGrabRepository.deleteByDynastyIdAndPositionType(dynastyId, request.getPositionType());

    log.info("王朝 {} 的 {} 官职抢夺时间已设置", dynastyId, request.getPositionType());
    return savedPosition;
  }

  /** 验证抢夺时间设置的合理性 */
  private void validateGrabTimeSettings(SetPositionGrabTimeRequest request) {
    LocalDateTime startTime = request.getGrabStartTime();
    LocalDateTime endTime = request.getGrabEndTime();
    LocalDate dutyDate = request.getDutyDate();

    // 验证开始时间早于结束时间
    if (!startTime.isBefore(endTime)) {
      throw new BusinessException("抢夺开始时间必须早于结束时间");
    }

    // 验证开始时间和结束时间在同一天
    if (!startTime.toLocalDate().equals(endTime.toLocalDate())) {
      throw new BusinessException("抢夺开始时间和结束时间必须在同一天");
    }

    // 验证任职日期在抢夺时间之后
    if (!dutyDate.isAfter(startTime.toLocalDate())) {
      throw new BusinessException("任职日期必须在抢夺时间之后");
    }

    // 验证禁用时段的有效性
    if (request.getDisabledTimeSlots() != null) {
      for (Integer slot : request.getDisabledTimeSlots()) {
        if (slot < 0 || slot > 23) {
          throw new BusinessException("时段必须在0-23之间");
        }
      }
    }
  }

  /** 抢夺官职 */
  @Transactional
  public PositionGrab grabPosition(Long dynastyId, Long accountId, GrabPositionRequest request) {
    Long userId = UserContext.getUserId();

    // 验证账号是否存在且属于当前用户
    GameAccount account =
        gameAccountRepository.findById(accountId).orElseThrow(() -> new BusinessException("账号不存在"));

    if (!account.getUserId().equals(userId)) {
      throw new BusinessException("无权操作此账号");
    }

    // 验证账号是否属于该王朝
    if (!dynastyId.equals(account.getDynastyId())) {
      throw new BusinessException("账号不属于该王朝");
    }

    // 验证王朝是否开启抢夺
    Dynasty dynasty =
        dynastyRepository.findById(dynastyId).orElseThrow(() -> new BusinessException("王朝不存在"));

    if (!dynasty.getGrabEnabled()) {
      throw new BusinessException("王朝未开启官职抢夺");
    }

    // 验证抢夺时间和条件
    validateGrabConditions(dynastyId, request);

    // 获取官职配置
    DynastyPosition position =
        dynastyPositionRepository
            .findByDynastyIdAndPositionType(dynastyId, request.getPositionType())
            .orElseThrow(() -> new BusinessException("官职配置不存在"));

    try {
      // 创建抢夺记录（利用数据库唯一约束防止并发问题）
      PositionGrab grab = new PositionGrab();
      grab.setDynastyId(dynastyId);
      grab.setPositionType(request.getPositionType());
      grab.setDutyDate(position.getDutyDate());
      grab.setTimeSlot(request.getTimeSlot());
      grab.setAccountId(accountId);

      PositionGrab savedGrab = positionGrabRepository.save(grab);

      log.info(
          "账号 {} 成功抢夺了王朝 {} 的 {} 官职时段 {}",
          account.getAccountName(),
          dynastyId,
          request.getPositionType(),
          request.getTimeSlot());

      return savedGrab;

    } catch (DataIntegrityViolationException e) {
      // 唯一约束冲突，说明该时段已被抢夺
      throw new BusinessException("该时段已被其他成员抢夺");
    }
  }

  /** 验证抢夺条件 */
  private void validateGrabConditions(Long dynastyId, GrabPositionRequest request) {
    DynastyPosition position =
        dynastyPositionRepository
            .findByDynastyIdAndPositionType(dynastyId, request.getPositionType())
            .orElseThrow(() -> new BusinessException("官职配置不存在"));

    LocalDateTime now = LocalDateTime.now();

    // 验证抢夺时间是否已设置
    if (position.getGrabStartTime() == null || position.getGrabEndTime() == null) {
      throw new BusinessException("官职抢夺时间未设置");
    }

    // 验证当前时间是否在抢夺时间范围内
    if (now.isBefore(position.getGrabStartTime()) || now.isAfter(position.getGrabEndTime())) {
      throw new BusinessException("当前时间不在抢夺时间范围内");
    }

    // 验证时段是否被禁用
    if (isTimeSlotDisabled(position, request.getTimeSlot())) {
      throw new BusinessException("该时段已被禁用，无法抢夺");
    }
  }

  /** 检查时段是否被禁用 */
  private boolean isTimeSlotDisabled(DynastyPosition position, Integer timeSlot) {
    if (position.getDisabledTimeSlots() == null || position.getDisabledTimeSlots().isEmpty()) {
      return false;
    }

    String[] disabledSlots = position.getDisabledTimeSlots().split(",");
    return Arrays.asList(disabledSlots).contains(timeSlot.toString());
  }

  /** 获取官职抢夺结果 */
  public PositionGrabResultResponse getGrabResults(Long dynastyId, LocalDate dutyDate) {
    // 验证王朝是否存在
    dynastyRepository.findById(dynastyId).orElseThrow(() -> new BusinessException("王朝不存在"));

    // 获取抢夺结果
    List<PositionGrab> allGrabs =
        positionGrabRepository.findByDynastyIdAndDutyDateOrderByPositionTypeAscTimeSlotAsc(
            dynastyId, dutyDate);

    Map<PositionType, List<PositionGrab>> grabsByType =
        allGrabs.stream().collect(Collectors.groupingBy(PositionGrab::getPositionType));

    // 获取可用时段信息
    Map<PositionType, List<Integer>> availableSlots = new HashMap<>();
    for (PositionType positionType : PositionType.values()) {
      List<Integer> available = getAvailableTimeSlots(dynastyId, positionType, dutyDate);
      availableSlots.put(positionType, available);
    }

    PositionGrabResultResponse response = new PositionGrabResultResponse();
    response.setDynastyId(dynastyId);
    response.setDutyDate(dutyDate);
    response.setGrabResults(grabsByType);
    response.setAvailableTimeSlots(availableSlots);

    return response;
  }

  /** 获取可用时段列表 */
  private List<Integer> getAvailableTimeSlots(
      Long dynastyId, PositionType positionType, LocalDate dutyDate) {
    // 获取官职配置
    DynastyPosition position =
        dynastyPositionRepository
            .findByDynastyIdAndPositionType(dynastyId, positionType)
            .orElse(null);

    if (position == null) {
      return Collections.emptyList();
    }

    // 获取已被抢夺的时段
    List<PositionGrab> grabs =
        positionGrabRepository.findByDynastyIdAndPositionTypeAndDutyDateOrderByTimeSlotAsc(
            dynastyId, positionType, dutyDate);
    Set<Integer> grabbedSlots =
        grabs.stream().map(PositionGrab::getTimeSlot).collect(Collectors.toSet());

    // 获取被禁用的时段
    final Set<Integer> disabledSlots;
    if (position.getDisabledTimeSlots() != null && !position.getDisabledTimeSlots().isEmpty()) {
      String[] slots = position.getDisabledTimeSlots().split(",");
      disabledSlots = Arrays.stream(slots).map(Integer::parseInt).collect(Collectors.toSet());
    } else {
      disabledSlots = new HashSet<>();
    }

    // 返回可用时段（0-23中排除已抢夺和被禁用的）
    return IntStream.range(0, 24)
        .filter(slot -> !grabbedSlots.contains(slot) && !disabledSlots.contains(slot))
        .boxed()
        .collect(Collectors.toList());
  }

  /** 验证用户是否为王朝天子 */
  private void validateEmperor(Long dynastyId) {
    Long userId = UserContext.getUserId();
    Dynasty dynasty =
        dynastyRepository.findById(dynastyId).orElseThrow(() -> new BusinessException("王朝不存在"));

    if (!dynasty.getEmperorId().equals(userId)) {
      throw new BusinessException("只有天子才能执行此操作");
    }
  }

  public DynastyPosition getPositionGrabTime(
      Long dynastyId, PositionType positionType) {
    return dynastyPositionRepository
        .findByDynastyIdAndPositionType(dynastyId, positionType)
        .orElse(null);
  }
}
