package com.app.gamehub.model;

import com.app.gamehub.entity.GameAccount;
import com.app.gamehub.entity.WarGroup;
import com.app.gamehub.exception.BusinessException;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

@Getter
@RequiredArgsConstructor
public enum TacticsType {

  /** 保己方三个小粮仓 */
  GUAN_DU_ONE {
    @Override
    protected List<TacticalArrangement> arrangementSorted(List<GameAccount> accounts) {
      List<TacticalArrangement> arrangements = new ArrayList<>();
      for (int i = 0; i < accounts.size(); i++) {
        if (arrangements.isEmpty() || arrangements.size() <= i % 3) {
          TacticalArrangement arrangement = new TacticalArrangement();
          arrangement.setWarGroup(new WarGroup());
          arrangement.setWarArrangements(new ArrayList<>());
          arrangements.add(arrangement);
        }
        // 分为三个小组，游戏账号已经根据账号伤害加成从大到小排列，加成排 1 2 3 的分别为三个队的组长，分别为第一、二、三组
        // 加成第 4 5 6的安排在第三二一组
        // 加成第 7 8 9 的安排在第一二三组
        // 以此类推，直到游戏账号全部安排完
        // 第一组任务：拿下我方一号小粮仓
        // 第二组任务：拿下我方三号小粮仓
        // 第三组任务：拿下我方二号小粮仓
        TacticalArrangement arrangement = arrangements.get(i % 3);
      }
      return new ArrayList<>();
    }
  },
  /** 保住己方小粮仓的同时拿下敌方三个小粮仓，放弃兵器坊和工匠坊。 */
  GUAN_DU_TWO {
    @Override
    protected List<TacticalArrangement> arrangementSorted(List<GameAccount> accounts) {
      // 游戏账号分为六组，数字代表账号伤害加成从大到小的顺序
      // 第一组（任务：拿下敌方一号小粮仓，二队驻防敌方三号小粮仓，三队驻防敌方二号小粮仓）：1 6 10 18 22
      // 第二组（任务：拿下敌方三号小粮仓，二队驻防敌方二号小粮仓，三队驻防敌方一号小粮仓）：2 5 11 17 23
      // 第三组（任务：拿下敌方二号小粮仓，二队驻防敌方一号小粮仓，三队驻防敌方三号小粮仓）：3 4 12 16 24
      // 第四组（任务：拿下我方一号小粮仓，二队驻防我方三号小粮仓，三队驻防我方二号小粮仓）：7 15 19 27 28
      // 第五组（任务：拿下我方三号小粮仓，二队驻防我方二号小粮仓，三队驻防我方一号小粮仓）：8 14 20 26 29
      // 第六组（任务：拿下我方二号小粮仓，二队驻防我方一号小粮仓，三队驻防我方三号小粮仓）：9 13 21 25 30
      return null;
    }
  },
  /** 主拿己方上下两个小粮仓，派兵拿下己方中间粮仓，拿下兵器坊和工匠坊，拿下敌方上下两个粮仓。 */
  GUAN_DU_THREE {
    @Override
    protected List<TacticalArrangement> arrangementSorted(List<GameAccount> accounts) {
      // 游戏账号分为六组，数字代表账号伤害加成从大到小的顺序
      // 第一组（任务：拿下兵器坊，二队驻防敌方一号小粮仓，三队驻防我方一号小粮仓，四队驻防工匠坊）：1 4 5 19 22
      // 第二组（任务：拿下工匠坊，二队驻防敌方三号小粮仓，三队驻防我方三号小粮仓，四队驻防兵器坊）：2 3 6 20 21
      // 第三组（任务：拿下敌方一号小粮仓，二队驻防兵器坊，三队驻防敌方三号小粮仓，四队驻防工匠坊）：7 14 15 26 27
      // 第四组（任务：拿下敌方三号小粮仓，二队驻防工匠坊，三队驻防敌方一号小粮仓，四队驻防兵器坊）：8 13 16 25 28
      // 第五组（任务：拿下我方一号小粮仓，二队驻防兵器坊，三队驻防我方三号小粮仓，四队驻防工匠坊）：9 12 17 24 29
      // 第六组（任务：拿下我方三号小粮仓，二队驻防工匠坊，三队驻防我方一号小粮仓，四队驻防兵器坊）：10 11 18 23 30
      return null;
    }
  },
  /** 拿下己方三个小粮仓，拿下兵器坊和敌方上方的小粮仓。 */
  GUAN_DU_FIVE {
    @Override
    protected List<TacticalArrangement> arrangementSorted(List<GameAccount> accounts) {
      // 游戏账号分为六组，数字代表账号伤害加成从大到小的顺序
        // 第一组（任务：拿下兵器坊，二队驻防敌方一号小粮仓，三队驻防我方一号小粮仓）：1 3 10 17 23
        // 第二组（任务：拿下工匠坊，二队驻防我方三号小粮仓，三队驻防我方二号小粮仓）：2 4 11 16 22
        // 第三组（任务：拿下敌方一号小粮仓，二队可以尝试集结敌方二号小粮仓或者驻防兵器坊，三队驻防工匠坊）：5 6 12 18 24
        // 第四组（任务：拿下我方一号小粮仓，主要驻防兵器坊和二号小粮仓）：9 13 21 27 28
        // 第五组（任务：拿下我方三号小粮仓，二队驻防工匠坊，三队驻防我方二号小粮仓）：8 15 20 25 29
        // 第六组（任务：拿下我方二号小粮仓，二队驻防我方三号小粮仓，三队驻防我方兵器坊）：7 14 19 26 30
      return null;
    }
  },
  ;

  public List<TacticalArrangement> arrangement(List<GameAccount> accounts) {
    if (CollectionUtils.isEmpty(accounts)) {
      return new ArrayList<>();
    }
    accounts.sort((a1, a2) -> a2.getDamageBonus().compareTo(a1.getDamageBonus()));
    if (accounts.size() > 30) {
      accounts = accounts.subList(0, 30);
    }
    return arrangementSorted(accounts);
  }

  protected List<TacticalArrangement> arrangementSorted(List<GameAccount> accounts) {
    throw new BusinessException("该战术暂未支持");
  }
}
