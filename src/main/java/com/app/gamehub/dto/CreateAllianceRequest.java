package com.app.gamehub.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

@Data
@Schema(description = "创建联盟请求")
public class CreateAllianceRequest {
    
    @NotBlank(message = "联盟名称不能为空")
    @Schema(description = "联盟名称")
    private String name;
    
    @NotNull(message = "区号不能为空")
    @Positive(message = "区号必须为正整数")
    @Schema(description = "区号")
    private Integer serverId;
}
