package com.app.gamehub.dto;

import com.app.gamehub.entity.PositionType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/** 查询官职抢夺时间请求 */
@Data
@Schema(description = "查询官职抢夺时间请求")
public class GetPositionGrabTimeRequest {

  @NotNull(message = "官职类型不能为空")
  @Schema(description = "官职类型", example = "TAI_WEI")
  private PositionType positionType;
}
