package com.app.gamehub.enums;

import com.app.gamehub.entity.WarGroup;
import com.app.gamehub.entity.WarType;
import java.util.List;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;

@Getter
@RequiredArgsConstructor
public enum TacticsType {

    ;
    private final WarType warType;

    public List<WarGroup> getWarGroups() {
        
    }
}
