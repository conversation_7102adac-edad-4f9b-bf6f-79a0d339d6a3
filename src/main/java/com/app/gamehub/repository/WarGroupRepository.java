package com.app.gamehub.repository;

import com.app.gamehub.entity.WarGroup;
import com.app.gamehub.entity.WarType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WarGroupRepository extends JpaRepository<WarGroup, Long> {
    
    List<WarGroup> findByAllianceIdAndWarTypeOrderByCreatedAtAsc(Long allianceId, WarType warType);
    
    List<WarGroup> findByAllianceIdOrderByWarTypeAscCreatedAtAsc(Long allianceId);
}
