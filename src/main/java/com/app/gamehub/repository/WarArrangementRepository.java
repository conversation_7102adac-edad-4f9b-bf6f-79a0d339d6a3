package com.app.gamehub.repository;

import com.app.gamehub.entity.WarArrangement;
import com.app.gamehub.entity.WarType;
import java.util.Collection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WarArrangementRepository extends JpaRepository<WarArrangement, Long> {
    
    List<WarArrangement> findByAllianceIdAndWarTypeOrderByWarGroupIdAscCreatedAtAsc(Long allianceId, WarType warType);
    
    List<WarArrangement> findByAllianceIdOrderByWarTypeAscWarGroupIdAscCreatedAtAsc(Long allianceId);
    
    List<WarArrangement> findByWarGroupIdOrderByCreatedAtAsc(Long warGroupId);

    List<WarArrangement> findByAccountIdOrderByWarTypeAscCreatedAtAsc(Long accountId);

    @Modifying
    @Query("DELETE FROM WarArrangement wa WHERE wa.allianceId = :allianceId AND wa.warType = :warType")
    void deleteByAllianceIdAndWarType(@Param("allianceId") Long allianceId, @Param("warType") WarType warType);
    
    @Modifying
    @Query("DELETE FROM WarArrangement wa WHERE wa.allianceId = :allianceId")
    void deleteByAllianceId(@Param("allianceId") Long allianceId);

    List<WarArrangement> findByAccountIdAndWarTypeIn(Long id, Collection<WarType> types);

    void deleteAllByAccountId(Long id);

    void deleteByAccountIdAndWarType(Long id, WarType type);

    boolean existsByAccountIdAndWarTypeIn(Long id, Collection<WarType> types);
}
