package com.app.gamehub.repository;

import com.app.gamehub.entity.DynastyPosition;
import com.app.gamehub.entity.PositionType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 王朝官职数据访问接口
 */
@Repository
public interface DynastyPositionRepository extends JpaRepository<DynastyPosition, Long> {
    
    /**
     * 根据王朝ID查找所有官职配置
     */
    List<DynastyPosition> findByDynastyId(Long dynastyId);
    
    /**
     * 根据王朝ID和官职类型查找官职配置
     */
    Optional<DynastyPosition> findByDynastyIdAndPositionType(Long dynastyId, PositionType positionType);
    
    /**
     * 根据王朝ID列表查找所有官职配置
     */
    List<DynastyPosition> findByDynastyIdIn(List<Long> dynastyIds);
}
