spring.application.name=game-hub-server

# Database Configuration
#spring.datasource.url=*****************************************
#spring.datasource.username=postgres
#spring.datasource.password=postgres
#spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
jwt.secret=3f5a7b2c9e1d4g6h8i0jklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!@#$%^&*()_+
jwt.expiration=86400000

# WeChat Mini Program Configuration
wechat.miniapp.appid=wxd6978ee5ee024cc1
wechat.miniapp.secret=878f56020a221bd56700da62dced5e11

# Swagger Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method

# Server Configuration
server.port=8080

# Logging Configuration
logging.level.com.app.gamehub=DEBUG
logging.level.org.springframework.security=DEBUG
