# 官职抢夺服务改进文档

## 概述
本次更新改进了 `PositionGrabService#grabPosition` 方法，从基于数据库异常捕获的并发控制改为基于锁的机制，并增加了新的业务限制。

## 主要改进

### 1. 并发控制机制改进

**原有方式**:
- 依赖数据库唯一约束
- 捕获 `DataIntegrityViolationException` 异常
- 无法准确区分异常原因

**新方式**:
- 使用 `ReentrantLock` 进行并发控制
- 锁的key为 `dynastyId + "_" + timeSlot`
- 在锁保护下进行业务逻辑检查

### 2. 新增业务限制

**一个用户在一种官职下只能抢夺一个时段**:
- 在抢夺前检查用户是否已在该官职类型下抢夺过时段
- 使用新增的Repository方法进行检查

### 3. 更精确的错误提示

**时段已被抢夺**:
- 明确提示："该时段的官职已被夺走"
- 区别于用户重复抢夺的错误

**用户重复抢夺**:
- 明确提示："您在该官职类型下已经抢夺了时段，每个用户在一种官职下只能抢夺一个时段"

## 技术实现

### 1. 锁管理

```java
// 用于管理抢夺锁的Map，key为 dynastyId + "_" + timeSlot
private final Map<String, ReentrantLock> grabLocks = new ConcurrentHashMap<>();

/**
 * 获取抢夺锁，锁的key为王朝ID + "_" + 时段
 */
private ReentrantLock getGrabLock(Long dynastyId, Integer timeSlot) {
    String lockKey = dynastyId + "_" + timeSlot;
    return grabLocks.computeIfAbsent(lockKey, k -> new ReentrantLock());
}
```

### 2. 新增Repository方法

```java
/**
 * 检查用户在指定王朝、官职类型和任职日期下是否已经抢夺了时段
 */
boolean existsByAccountIdAndDynastyIdAndPositionTypeAndDutyDate(
        Long accountId, Long dynastyId, PositionType positionType, LocalDate dutyDate);
```

### 3. 改进后的抢夺逻辑

```java
// 获取抢夺锁
ReentrantLock lock = getGrabLock(dynastyId, request.getTimeSlot());
lock.lock();

try {
    // 检查用户在该官职类型下是否已经抢夺了时段
    boolean hasGrabbed = positionGrabRepository.existsByAccountIdAndDynastyIdAndPositionTypeAndDutyDate(
        accountId, dynastyId, request.getPositionType(), position.getDutyDate());
    
    if (hasGrabbed) {
        throw new BusinessException("您在该官职类型下已经抢夺了时段，每个用户在一种官职下只能抢夺一个时段");
    }
    
    // 检查该时段是否已被抢夺
    boolean isSlotTaken = positionGrabRepository.existsByDynastyIdAndPositionTypeAndDutyDateAndTimeSlot(
        dynastyId, request.getPositionType(), position.getDutyDate(), request.getTimeSlot());
    
    if (isSlotTaken) {
        throw new BusinessException("该时段的官职已被夺走");
    }

    // 创建抢夺记录
    // ...
} finally {
    lock.unlock();
}
```

## 优势

### 1. 性能优化
- 避免了数据库异常的开销
- 减少了不必要的数据库操作
- 锁的粒度更细（按王朝+时段）

### 2. 错误处理改进
- 更精确的错误信息
- 更好的用户体验
- 便于前端进行相应的处理

### 3. 业务逻辑清晰
- 明确的业务规则检查
- 代码逻辑更易理解和维护
- 便于后续扩展

### 4. 并发安全
- 使用成熟的锁机制
- 避免了竞态条件
- 保证了数据一致性

## 测试覆盖

### 新增测试用例

1. **grabPosition_TimeSlotAlreadyTaken_ThrowsException**
   - 测试时段已被抢夺的情况
   - 验证错误信息的准确性

2. **grabPosition_UserAlreadyGrabbedInSamePositionType_ThrowsException**
   - 测试用户重复抢夺的情况
   - 验证一个用户在一种官职下只能抢夺一个时段的限制

### 更新的测试用例

1. **grabPosition_Success**
   - 添加了对新检查逻辑的mock
   - 确保成功场景的完整性

## 向后兼容性

- 接口签名保持不变
- 返回值类型不变
- 只是内部实现逻辑的优化
- 完全向后兼容

## 部署注意事项

1. **内存使用**
   - 锁Map会随着王朝和时段的增加而增长
   - 建议监控内存使用情况
   - 可考虑添加锁的清理机制

2. **性能监控**
   - 监控锁的竞争情况
   - 关注抢夺操作的响应时间

3. **日志记录**
   - 保持了原有的成功日志记录
   - 错误信息更加明确

## 未来优化建议

1. **锁清理机制**
   - 定期清理不再使用的锁
   - 避免内存泄漏

2. **分布式锁**
   - 如果需要支持多实例部署
   - 可考虑使用Redis等分布式锁

3. **性能优化**
   - 可考虑使用读写锁
   - 进一步优化并发性能
