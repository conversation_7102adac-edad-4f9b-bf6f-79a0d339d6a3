# 游戏战术室 API 使用指南

## 概述

这是一个游戏战术室（微信小程序）的后端API系统，支持三国·冰河时代游戏的联盟管理和战事安排功能。

## 技术栈

- Spring Boot 3.5.4
- Java 21
- PostgreSQL
- JWT认证
- Swagger API文档
- 微信小程序SDK

## 快速开始

### 1. 环境准备

确保已安装：
- Java 21
- PostgreSQL
- Maven

### 2. 数据库配置

创建PostgreSQL数据库：
```sql
CREATE DATABASE game_hub;
```

修改 `application.properties` 中的数据库连接信息。

### 3. 微信小程序配置

在 `application.properties` 中配置微信小程序的 AppID 和 Secret：
```properties
wechat.miniapp.appid=your_app_id
wechat.miniapp.secret=your_app_secret
```

### 4. 启动应用

```bash
mvn spring-boot:run
```

应用启动后，访问 http://localhost:8080/swagger-ui.html 查看API文档。

## API 接口说明

### 认证相关

#### 1. 微信小程序登录
- **POST** `/api/auth/login`
- 请求体：
```json
{
  "code": "微信小程序登录凭证",
  "nickname": "用户昵称",
  "avatarUrl": "用户头像URL"
}
```

#### 2. 获取用户信息
- **GET** `/api/auth/user-info`
- 需要JWT认证

#### 3. 获取用户完整信息
- **GET** `/api/auth/user-complete-info`
- 返回用户信息、联盟列表、账号列表

### 联盟管理

#### 1. 创建联盟
- **POST** `/api/alliances`
- 自动生成6位联盟编码

#### 2. 更新联盟
- **PUT** `/api/alliances/{allianceId}`

#### 3. 删除联盟
- **DELETE** `/api/alliances/{allianceId}`

#### 4. 转交联盟
- **POST** `/api/alliances/{allianceId}/transfer`

### 游戏账号管理

#### 1. 创建游戏账号
- **POST** `/api/game-accounts`
- 每个用户在每个区最多2个账号

#### 2. 更新游戏账号
- **PUT** `/api/game-accounts/{accountId}`

#### 3. 删除游戏账号
- **DELETE** `/api/game-accounts/{accountId}`

### 联盟成员管理

#### 1. 申请加入联盟
- **POST** `/api/alliance-members/apply`

#### 2. 处理加入申请
- **POST** `/api/alliance-members/applications/{applicationId}/process`

#### 3. 移除成员
- **DELETE** `/api/alliance-members/accounts/{accountId}`

### 战事管理

#### 1. 申请参加战事
- **POST** `/api/wars/apply`
- 只能申请官渡一或官渡二

#### 2. 处理战事申请
- **POST** `/api/wars/applications/{applicationId}/process`

#### 3. 移动战事申请
- **POST** `/api/wars/applications/{applicationId}/move`

### 战事分组管理

#### 1. 创建战事分组
- **POST** `/api/war-groups`

#### 2. 更新战事分组
- **PUT** `/api/war-groups/{groupId}`

#### 3. 删除战事分组
- **DELETE** `/api/war-groups/{groupId}`

#### 4. 安排成员到分组
- **POST** `/api/war-groups/arrange`

#### 5. 清空战事安排
- **DELETE** `/api/war-groups/alliances/{allianceId}/arrangements`

### 查询接口

#### 1. 联盟详情
- **GET** `/api/query/alliances/{allianceId}/detail`

#### 2. 账号申请状态
- **GET** `/api/query/accounts/{accountId}/alliance-applications`
- **GET** `/api/query/accounts/{accountId}/war-applications`

#### 3. 盟主查询申请列表
- **GET** `/api/query/alliances/{allianceId}/pending-applications`
- **GET** `/api/query/alliances/{allianceId}/war-applications`

## 数据模型

### 核心实体

1. **User** - 微信小程序用户
2. **Alliance** - 联盟
3. **GameAccount** - 游戏账号
4. **AllianceApplication** - 联盟申请
5. **WarApplication** - 战事申请
6. **WarGroup** - 战事分组
7. **WarArrangement** - 战事安排

### 业务规则

1. 每个用户在每个区最多创建2个游戏账号
2. 每个联盟最多100个正式成员
3. 账号只能加入同一个区的联盟
4. 只能申请参加官渡一或官渡二战事
5. 不能同时申请官渡一和官渡二
6. 攻城和守城由盟主直接安排

## 测试

运行测试：
```bash
mvn test
```

## 部署

使用Docker Compose部署：
```bash
docker-compose up -d
```

## 注意事项

1. 所有Long类型字段在API响应中都会转换为String类型
2. 实体ID使用雪花算法生成
3. 除登录接口外，其他接口都需要JWT认证
4. Swagger文档中Long类型字段使用String表示
